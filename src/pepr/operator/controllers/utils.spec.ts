/**
 * Copyright 2024 Defense Unicorns
 * SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial
 */

import { Logger } from "pino";
import { beforeEach, afterEach, describe, expect, it, vi } from "vitest";
import { kind } from "pepr";
import { retryWithDelay } from "./utils";

// Mock the K8s Get function
const mockGet = vi.fn().mockImplementation(resource => Promise.resolve(resource));

// Mock the pepr module
vi.mock("pepr", () => {
  return {
    K8s: () => ({
      Get: mockGet,
    }),
    kind: {
      Namespace: "Namespace",
    },
  };
});

describe("retryWithDelay", () => {
  let mockLogger: Logger;

  beforeEach(() => {
    mockLogger = {
      warn: vi.fn(),
      level: "info",
      fatal: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
      debug: vi.fn(),
      trace: vi.fn(),
    } as unknown as Logger;
  });

  beforeEach(() => {});

  it("should succeed on the first attempt", async () => {
    const mockFn = vi.fn<() => Promise<string>>().mockResolvedValue("Success");

    const result = await retryWithDelay(mockFn, mockLogger);

    expect(result).toBe("Success");
    expect(mockFn).toHaveBeenCalledTimes(1); // Called only once
    expect(mockLogger.warn).not.toHaveBeenCalled(); // No warnings logged
  });

  it("should retry on failure and eventually succeed", async () => {
    const mockFn = vi
      .fn<() => Promise<string>>()
      .mockRejectedValueOnce(new Error("Fail on 1st try")) // Fail first attempt
      .mockResolvedValue("Success"); // Succeed on retry

    const result = await retryWithDelay(mockFn, mockLogger, 3, 100);

    expect(result).toBe("Success");
    expect(mockFn).toHaveBeenCalledTimes(2); // Called twice (1 fail + 1 success)
    expect(mockLogger.warn).toHaveBeenCalledTimes(1); // Warned once for the retry
    expect(mockLogger.warn).toHaveBeenCalledWith(
      expect.stringContaining("Attempt 1 of spy failed, retrying in 100ms."),
      expect.objectContaining({ error: expect.any(String) }),
    );
  });

  it("should retry when function rejects without an error", async () => {
    const mockFn = vi
      .fn<() => Promise<string>>()
      .mockRejectedValueOnce(undefined) // Rejected with no error
      .mockResolvedValue("Success"); // Succeed on retry

    const result = await retryWithDelay(mockFn, mockLogger, 3, 100);

    expect(result).toBe("Success");
    expect(mockFn).toHaveBeenCalledTimes(2); // Called twice (1 fail + 1 success)
    expect(mockLogger.warn).toHaveBeenCalledTimes(1);
    expect(mockLogger.warn).toHaveBeenCalledWith(
      expect.stringContaining("Attempt 1 of spy failed, retrying in 100ms."),
      expect.objectContaining({ error: "Unknown Error" }),
    );
  });

  it("should throw the original error after max retries", async () => {
    const error = new Error("Persistent failure");
    const mockFn = vi.fn<() => Promise<string>>().mockRejectedValue(error); // Always fails

    await expect(retryWithDelay(mockFn, mockLogger, 3, 100)).rejects.toThrow("Persistent failure");

    expect(mockFn).toHaveBeenCalledTimes(3); // Retries up to the limit
    expect(mockLogger.warn).toHaveBeenCalledTimes(2); // Logged for each failure except the final one
    expect(mockLogger.warn).toHaveBeenCalledWith(
      expect.stringContaining("Attempt 1 of spy failed, retrying in 100ms."),
      expect.objectContaining({ error: error.message }),
    );
    expect(mockLogger.warn).toHaveBeenCalledWith(
      expect.stringContaining("Attempt 2 of spy failed, retrying in 100ms."),
      expect.objectContaining({ error: error.message }),
    );
  });
});

describe("test validateNamespace", () => {
  beforeEach(() => {
    process.env.PEPR_WATCH_MODE = "true";
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it("should return namespace object when namespace is found", async () => {
    const mockNamespace = { metadata: { name: "istio-egress-gateway" } } as kind.Namespace;

    const getNsMock = vi
      .fn<() => Promise<kind.Namespace>>()
      .mockResolvedValue(mockNamespace);

    const result = await validateEgressGatewayNamespace();
    expect(result).toEqual(mockNamespace);
  });

  it("should return null if get egress gateway namespace missing, with missingAllowed", async () => {
    const errorMessage = "Unable to get the egress gateway namespace istio-egress-gateway.";

    const getNsMock = vi
      .fn<() => Promise<kind.Namespace>>()
      .mockRejectedValue({ message: errorMessage, status: 404 });

    updateEgressMocks({
      ...defaultEgressMocks,
      getNsMock,
    });

    const result = await validateEgressGatewayNamespace(true);
    expect(result).toBeNull();
  });
  
  it("should err if get egress gateway namespace missing, no missingAllowed", async () => {
    const errorMessage = "Unable to get the egress gateway namespace istio-egress-gateway.";

    const getNsMock = vi
      .fn<() => Promise<kind.Namespace>>()
      .mockRejectedValue({ message: errorMessage, status: 404 });

    updateEgressMocks({
      ...defaultEgressMocks,
      getNsMock,
    });

    await expect(validateEgressGatewayNamespace()).rejects.toThrow(errorMessage);
  });

  it("should err if get egress gateway namespace fails, with missingAllowed", async () => {
    const errorMessage = "Unable to get the egress gateway namespace istio-egress-gateway.";

    const getNsMock = vi
      .fn<() => Promise<kind.Namespace>>()
      .mockRejectedValue({ message: errorMessage, status: 401 });

    updateEgressMocks({
      ...defaultEgressMocks,
      getNsMock,
    });

    await expect(validateEgressGatewayNamespace(true)).rejects.toThrow(errorMessage);
  });
})